<template>
  <div class="component-verification-example">
    <h2>基于组件的验证系统示例</h2>
    <p class="description">
      这个示例展示了如何使用新的 <code>verifyWithComponent</code> 方法，
      该方法模仿原有的 <code>verify</code> 方法，但使用 TurnstileComponent.vue 组件进行验证。
    </p>
    
    <div class="example-section">
      <h3>与原 verify 方法对比</h3>
      <div class="comparison-grid">
        <div class="method-card">
          <h4>原 verify 方法</h4>
          <div class="code-block">
            <pre><code>await VerificationMgr.instance.verify(
  "password_login",
  (result) => {
    if (result && result.success) {
      console.log("验证成功");
    }
  }
);</code></pre>
          </div>
          <div class="features">
            <p>✅ 使用现有的验证组件</p>
            <p>✅ 稳定可靠</p>
            <p>❌ UI 样式固定</p>
            <p>❌ 扩展性有限</p>
          </div>
        </div>
        
        <div class="method-card">
          <h4>新 verifyWithComponent 方法</h4>
          <div class="code-block">
            <pre><code>await VerificationMgr.instance.verifyWithComponent(
  "password_login",
  (result) => {
    if (result && result.success) {
      console.log("验证成功");
    }
  }
);</code></pre>
          </div>
          <div class="features">
            <p>✅ 使用 TurnstileComponent.vue</p>
            <p>✅ 现代化 UI</p>
            <p>✅ 高度可定制</p>
            <p>✅ 更好的用户体验</p>
          </div>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>验证测试</h3>
      <div class="test-grid">
        <div class="test-group">
          <h4>使用原 verify 方法</h4>
          <div class="button-group">
            <button @click="testOriginalLogin" :disabled="isLoading">
              {{ isLoading ? "验证中..." : "登录验证 (原方法)" }}
            </button>
            <button @click="testOriginalGetCode" :disabled="isLoading">
              获取验证码 (原方法)
            </button>
          </div>
        </div>
        
        <div class="test-group">
          <h4>使用新 verifyWithComponent 方法</h4>
          <div class="button-group">
            <button @click="testComponentLogin" :disabled="isLoading">
              {{ isLoading ? "验证中..." : "登录验证 (组件方法)" }}
            </button>
            <button @click="testComponentGetCode" :disabled="isLoading">
              获取验证码 (组件方法)
            </button>
            <button @click="testComponentWithdraw" :disabled="isLoading">
              提现验证 (组件方法)
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>验证类型切换</h3>
      <div class="switch-controls">
        <div class="current-type">
          当前验证类型: <strong>{{ currentVerificationType }}</strong>
        </div>
        <div class="switch-buttons">
          <button @click="switchToCloudflare" :disabled="isLoading">
            切换到 Cloudflare
          </button>
          <button @click="switchToGeetest" :disabled="isLoading">
            切换到 Geetest
          </button>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>验证结果</h3>
      <div class="result-display">
        <div v-if="lastResult" class="result-content">
          <div class="result-header">
            <span :class="['result-status', lastResult.success ? 'success' : 'failed']">
              {{ lastResult.success ? '✅ 成功' : '❌ 失败' }}
            </span>
            <span class="result-type">{{ lastResult.type }}</span>
          </div>
          <pre class="result-data">{{ JSON.stringify(lastResult, null, 2) }}</pre>
        </div>
        <p v-else class="no-result">暂无验证结果</p>
      </div>
    </div>

    <div class="example-section">
      <h3>使用说明</h3>
      <div class="usage-guide">
        <h4>1. 基本使用</h4>
        <div class="code-block">
          <pre><code>import { executeComponentVerification } from "@/utils/VerificationMgr";

// 使用便捷函数
await executeComponentVerification(
  "password_login",
  (result) => {
    if (result && result.success) {
      // 处理验证成功
      console.log("验证数据:", result.data);
    } else {
      // 处理验证失败
      console.log("验证失败");
    }
  }
);</code></pre>
        </div>

        <h4>2. 直接调用</h4>
        <div class="code-block">
          <pre><code>import { VerificationMgr } from "@/utils/VerificationMgr";

// 直接调用实例方法
await VerificationMgr.instance.verifyWithComponent(
  "password_login",
  (result) => {
    // 处理结果
  }
);</code></pre>
        </div>

        <h4>3. 在组件中使用</h4>
        <div class="code-block">
          <pre><code>&lt;script setup lang="ts"&gt;
import { executeComponentVerification } from "@/utils/VerificationMgr";

const handleLogin = async () => {
  await executeComponentVerification(
    "password_login",
    async (result) => {
      if (result && result.success) {
        // 使用验证结果进行登录
        await performLogin(result.data);
      }
    }
  );
};
&lt;/script&gt;</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { showToast } from "vant";
import { 
  executeVerification, 
  executeComponentVerification,
  switchVerificationType,
  verificationMgr,
  VERIFICATION_TYPE,
  type VerificationResult
} from "@/utils/VerificationMgr";

// 状态管理
const isLoading = ref(false);
const lastResult = ref<VerificationResult | null>(null);

// 当前验证类型
const currentVerificationType = computed(() => {
  return verificationMgr.getVerificationTypeName();
});

// 处理验证结果
const handleVerificationResult = (result: VerificationResult | false, testName: string) => {
  if (result && typeof result === 'object') {
    lastResult.value = result;
    
    if (result.success) {
      showToast(`${testName} 验证成功`);
    } else {
      showToast(`${testName} 验证失败: ${result.error || '未知错误'}`);
    }
  } else {
    lastResult.value = {
      success: false,
      type: verificationMgr.getCurrentVerificationType(),
      error: "验证失败",
    };
    showToast(`${testName} 验证失败`);
  }
};

// 原方法测试
const testOriginalLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    await executeVerification(
      "password_login",
      (result) => handleVerificationResult(result, "登录验证 (原方法)")
    );
  } catch (error) {
    console.error("原方法登录验证失败:", error);
    showToast("测试失败");
  } finally {
    isLoading.value = false;
  }
};

const testOriginalGetCode = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    await executeVerification(
      "phone_login_code",
      (result) => handleVerificationResult(result, "获取验证码 (原方法)"),
      { phone: "+1234567890" }
    );
  } catch (error) {
    console.error("原方法获取验证码失败:", error);
    showToast("测试失败");
  } finally {
    isLoading.value = false;
  }
};

// 组件方法测试
const testComponentLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    await executeComponentVerification(
      "password_login",
      (result) => handleVerificationResult(result, "登录验证 (组件方法)")
    );
  } catch (error) {
    console.error("组件方法登录验证失败:", error);
    showToast("测试失败");
  } finally {
    isLoading.value = false;
  }
};

const testComponentGetCode = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    await executeComponentVerification(
      "phone_login_code",
      (result) => handleVerificationResult(result, "获取验证码 (组件方法)"),
      { phone: "+1234567890" }
    );
  } catch (error) {
    console.error("组件方法获取验证码失败:", error);
    showToast("测试失败");
  } finally {
    isLoading.value = false;
  }
};

const testComponentWithdraw = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  try {
    await executeComponentVerification(
      "withdraw",
      (result) => handleVerificationResult(result, "提现验证 (组件方法)")
    );
  } catch (error) {
    console.error("组件方法提现验证失败:", error);
    showToast("测试失败");
  } finally {
    isLoading.value = false;
  }
};

// 切换验证类型
const switchToCloudflare = () => {
  switchVerificationType(VERIFICATION_TYPE.CLOUDFLARE);
  showToast("已切换到 Cloudflare 验证");
};

const switchToGeetest = () => {
  switchVerificationType(VERIFICATION_TYPE.GEETEST);
  showToast("已切换到 Geetest 验证");
};

onMounted(() => {
  console.log("组件验证示例已加载");
  console.log("当前验证类型:", currentVerificationType.value);
});
</script>

<style scoped lang="scss">
.component-verification-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 10px;
  }
  
  .description {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
    
    code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
    }
  }
  
  h3 {
    color: #555;
    margin-bottom: 15px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
  }
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.method-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  
  h4 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      white-space: pre-wrap;
    }
  }
  
  .features {
    p {
      margin: 5px 0;
      font-size: 14px;
    }
  }
}

.test-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.test-group {
  h4 {
    color: #555;
    margin-bottom: 15px;
  }
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  button {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    background: #007bff;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 14px;
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
    
    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}

.switch-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  
  .current-type {
    font-size: 16px;
    color: #333;
  }
  
  .switch-buttons {
    display: flex;
    gap: 10px;
    
    button {
      padding: 8px 16px;
      border: 1px solid #007bff;
      border-radius: 4px;
      background: white;
      color: #007bff;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover:not(:disabled) {
        background: #007bff;
        color: white;
      }
      
      &:disabled {
        border-color: #ccc;
        color: #ccc;
        cursor: not-allowed;
      }
    }
  }
}

.result-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 20px;
  
  .result-content {
    .result-header {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
      
      .result-status {
        font-weight: bold;
        
        &.success {
          color: #28a745;
        }
        
        &.failed {
          color: #dc3545;
        }
      }
      
      .result-type {
        background: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #495057;
      }
    }
    
    .result-data {
      background: #ffffff;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 300px;
      overflow-y: auto;
    }
  }
  
  .no-result {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    margin: 0;
  }
}

.usage-guide {
  h4 {
    color: #333;
    margin: 20px 0 10px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.5;
      white-space: pre-wrap;
    }
  }
}
</style>
