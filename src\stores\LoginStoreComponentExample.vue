<template>
  <div class="login-store-component-example">
    <h2>Login Store 组件验证方法示例</h2>
    <p class="description">
      展示如何使用新的基于 TurnstileComponent 的登录验证方法
    </p>

    <div class="example-section">
      <h3>方法对比</h3>
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>功能</th>
              <th>原方法</th>
              <th>新组件方法</th>
              <th>主要区别</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>验证处理</td>
              <td><code>handleVerification</code></td>
              <td><code>handleComponentVerification</code></td>
              <td>使用 TurnstileComponent.vue</td>
            </tr>
            <tr>
              <td>密码登录</td>
              <td><code>handlePasswordLogin</code></td>
              <td><code>handlePasswordLoginWithComponent</code></td>
              <td>支持自定义验证 UI</td>
            </tr>
            <tr>
              <td>验证码登录</td>
              <td><code>handleCodeLogin</code></td>
              <td><code>handleCodeLoginWithComponent</code></td>
              <td>更好的用户体验</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="example-section">
      <h3>测试登录功能</h3>
      <div class="login-test-form">
        <div class="form-group">
          <label>手机号:</label>
          <input v-model="testPhone" placeholder="请输入手机号" />
        </div>
        
        <div class="form-group">
          <label>密码:</label>
          <input v-model="testPassword" type="password" placeholder="请输入密码" />
        </div>
        
        <div class="form-group">
          <label>验证码:</label>
          <input v-model="testVerCode" placeholder="请输入验证码" />
        </div>

        <div class="form-group">
          <label>验证主题:</label>
          <select v-model="verificationTheme">
            <option value="auto">自动</option>
            <option value="light">浅色</option>
            <option value="dark">深色</option>
          </select>
        </div>

        <div class="form-group">
          <label>验证尺寸:</label>
          <select v-model="verificationSize">
            <option value="normal">正常</option>
            <option value="compact">紧凑</option>
            <option value="flexible">灵活</option>
          </select>
        </div>
      </div>

      <div class="button-groups">
        <div class="button-group">
          <h4>原方法测试</h4>
          <button @click="testOriginalPasswordLogin" :disabled="isLoading">
            {{ isLoading ? "登录中..." : "密码登录 (原方法)" }}
          </button>
          <button @click="testOriginalCodeLogin" :disabled="isLoading">
            {{ isLoading ? "登录中..." : "验证码登录 (原方法)" }}
          </button>
        </div>

        <div class="button-group">
          <h4>新组件方法测试</h4>
          <button @click="testComponentPasswordLogin" :disabled="isLoading">
            {{ isLoading ? "登录中..." : "密码登录 (组件方法)" }}
          </button>
          <button @click="testComponentCodeLogin" :disabled="isLoading">
            {{ isLoading ? "登录中..." : "验证码登录 (组件方法)" }}
          </button>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>测试结果</h3>
      <div class="result-display">
        <div v-if="testResults.length > 0">
          <div v-for="(result, index) in testResults" :key="index" class="test-result">
            <div class="result-header">
              <span :class="['status', result.success ? 'success' : 'failed']">
                {{ result.success ? '✅' : '❌' }}
              </span>
              <span class="method-name">{{ result.methodName }}</span>
              <span class="timestamp">{{ result.timestamp }}</span>
            </div>
            <div class="result-details">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <p v-else class="no-results">暂无测试结果</p>
      </div>
    </div>

    <div class="example-section">
      <h3>使用示例代码</h3>
      <div class="code-examples">
        <h4>1. 基础使用 - 密码登录</h4>
        <div class="code-block">
          <pre><code>// 使用新的组件验证方法
import { useLoginStore } from "@/stores/login";

const loginStore = useLoginStore();

// 密码登录 (使用 TurnstileComponent)
await loginStore.handlePasswordLoginWithComponent(
  { password: "your-password", phone: "+1234567890" },
  {
    title: "Password Login",
    description: "Please complete verification to login",
    cloudflare: {
      theme: "auto",
      size: "normal",
      appearance: "always"
    }
  }
);</code></pre>
        </div>

        <h4>2. 验证码登录</h4>
        <div class="code-block">
          <pre><code>// 验证码登录 (使用 TurnstileComponent)
await loginStore.handleCodeLoginWithComponent(
  { verCode: "123456", phone: "+1234567890" },
  {
    title: "Code Login",
    description: "Please complete verification to login",
    cloudflare: {
      theme: "dark",
      size: "compact"
    }
  }
);</code></pre>
        </div>

        <h4>3. 直接使用验证方法</h4>
        <div class="code-block">
          <pre><code>// 直接使用组件验证方法
await loginStore.handleComponentVerification(
  "password_login",
  async (verificationResult) => {
    // 处理验证结果
    console.log("验证成功:", verificationResult);
  },
  false, // 不使用无感验证
  {
    title: "Custom Verification",
    description: "Custom verification description",
    showCancelButton: true,
    cloudflare: {
      theme: "light",
      size: "flexible",
      appearance: "interaction-only"
    }
  }
);</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { showToast } from "vant";
import { useLoginStore } from "@/stores/login";

// 状态管理
const isLoading = ref(false);
const testResults = ref<Array<{
  methodName: string;
  success: boolean;
  data: any;
  timestamp: string;
}>>([]);

// 测试数据
const testPhone = ref("+1234567890");
const testPassword = ref("test123456");
const testVerCode = ref("123456");
const verificationTheme = ref<"light" | "dark" | "auto">("auto");
const verificationSize = ref<"normal" | "flexible" | "compact">("normal");

// Login Store
const loginStore = useLoginStore();

// 添加测试结果
const addTestResult = (methodName: string, success: boolean, data: any) => {
  testResults.value.unshift({
    methodName,
    success,
    data,
    timestamp: new Date().toLocaleTimeString(),
  });
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }
};

// 测试原方法 - 密码登录
const testOriginalPasswordLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    // 设置手机号
    loginStore.userPhone = testPhone.value;
    
    await loginStore.handlePasswordLogin({
      password: testPassword.value,
      phone: testPhone.value,
    });
    
    addTestResult("handlePasswordLogin (原方法)", true, {
      method: "handlePasswordLogin",
      phone: testPhone.value,
      success: true,
    });
    showToast("原方法密码登录测试成功");
  } catch (error) {
    console.error("原方法密码登录测试失败:", error);
    addTestResult("handlePasswordLogin (原方法)", false, {
      method: "handlePasswordLogin",
      error: error.message,
    });
    showToast("原方法密码登录测试失败");
  } finally {
    isLoading.value = false;
  }
};

// 测试原方法 - 验证码登录
const testOriginalCodeLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    // 设置手机号
    loginStore.userPhone = testPhone.value;
    
    await loginStore.handleCodeLogin({
      verCode: testVerCode.value,
      phone: testPhone.value,
    });
    
    addTestResult("handleCodeLogin (原方法)", true, {
      method: "handleCodeLogin",
      phone: testPhone.value,
      verCode: testVerCode.value,
      success: true,
    });
    showToast("原方法验证码登录测试成功");
  } catch (error) {
    console.error("原方法验证码登录测试失败:", error);
    addTestResult("handleCodeLogin (原方法)", false, {
      method: "handleCodeLogin",
      error: error.message,
    });
    showToast("原方法验证码登录测试失败");
  } finally {
    isLoading.value = false;
  }
};

// 测试组件方法 - 密码登录
const testComponentPasswordLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    // 设置手机号
    loginStore.userPhone = testPhone.value;
    
    await loginStore.handlePasswordLoginWithComponent(
      {
        password: testPassword.value,
        phone: testPhone.value,
      },
      {
        title: "Password Login with Component",
        description: "Testing password login with TurnstileComponent verification",
        cloudflare: {
          theme: verificationTheme.value,
          size: verificationSize.value,
          appearance: "always",
        },
      }
    );
    
    addTestResult("handlePasswordLoginWithComponent (组件方法)", true, {
      method: "handlePasswordLoginWithComponent",
      phone: testPhone.value,
      theme: verificationTheme.value,
      size: verificationSize.value,
      success: true,
    });
    showToast("组件方法密码登录测试成功");
  } catch (error) {
    console.error("组件方法密码登录测试失败:", error);
    addTestResult("handlePasswordLoginWithComponent (组件方法)", false, {
      method: "handlePasswordLoginWithComponent",
      error: error.message,
    });
    showToast("组件方法密码登录测试失败");
  } finally {
    isLoading.value = false;
  }
};

// 测试组件方法 - 验证码登录
const testComponentCodeLogin = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    // 设置手机号
    loginStore.userPhone = testPhone.value;
    
    await loginStore.handleCodeLoginWithComponent(
      {
        verCode: testVerCode.value,
        phone: testPhone.value,
      },
      {
        title: "Code Login with Component",
        description: "Testing code login with TurnstileComponent verification",
        cloudflare: {
          theme: verificationTheme.value,
          size: verificationSize.value,
          appearance: "always",
        },
      }
    );
    
    addTestResult("handleCodeLoginWithComponent (组件方法)", true, {
      method: "handleCodeLoginWithComponent",
      phone: testPhone.value,
      verCode: testVerCode.value,
      theme: verificationTheme.value,
      size: verificationSize.value,
      success: true,
    });
    showToast("组件方法验证码登录测试成功");
  } catch (error) {
    console.error("组件方法验证码登录测试失败:", error);
    addTestResult("handleCodeLoginWithComponent (组件方法)", false, {
      method: "handleCodeLoginWithComponent",
      error: error.message,
    });
    showToast("组件方法验证码登录测试失败");
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped lang="scss">
.login-store-component-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 10px;
  }
  
  .description {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
  }
  
  h3 {
    color: #555;
    margin-bottom: 15px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
  }
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.comparison-table {
  overflow-x: auto;
  
  table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    
    th, td {
      padding: 12px;
      text-align: left;
      border: 1px solid #ddd;
    }
    
    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #333;
    }
    
    code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
  }
}

.login-test-form {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .form-group {
    margin-bottom: 15px;
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
    }
    
    input, select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #007bff;
      }
    }
  }
}

.button-groups {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.button-group {
  h4 {
    color: #333;
    margin-bottom: 15px;
    text-align: center;
  }
  
  button {
    width: 100%;
    padding: 12px 20px;
    margin-bottom: 10px;
    border: none;
    border-radius: 6px;
    background: #007bff;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 14px;
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
    
    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}

.result-display {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
  
  .no-results {
    color: #999;
    text-align: center;
    margin: 0;
  }
}

.test-result {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 6px;
  background: #f9f9f9;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    
    .status {
      font-size: 16px;
      
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
    
    .method-name {
      font-weight: bold;
      color: #333;
      flex: 1;
    }
    
    .timestamp {
      color: #666;
      font-size: 12px;
    }
  }
  
  .result-details {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.code-examples {
  h4 {
    color: #333;
    margin: 20px 0 10px 0;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  .code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.5;
      white-space: pre-wrap;
    }
  }
}
</style>
