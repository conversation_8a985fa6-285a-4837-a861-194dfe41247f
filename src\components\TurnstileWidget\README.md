# TurnstileComponent

Cloudflare Turnstile 验证组件，提供人机验证功能。

## 功能特性

- 🔒 **安全验证**: 基于 Cloudflare Turnstile 的人机验证
- 🎨 **主题支持**: 支持 light、dark、auto 三种主题
- 📱 **响应式**: 支持多种尺寸和外观模式
- 🔧 **高度可配置**: 支持丰富的配置选项
- 🚀 **TypeScript**: 完整的类型定义和类型安全
- 🎯 **事件丰富**: 提供完整的生命周期事件

## 基本用法

```vue
<template>
  <TurnstileComponent
    :sitekey="TURNSTILE_SITE_KEY"
    theme="auto"
    size="normal"
    @callback="handleSuccess"
    @errorCallback="handleError"
  />
</template>

<script setup lang="ts">
import TurnstileComponent from "@/components/TurnstileWidget/TurnstileComponent.vue";

const TURNSTILE_SITE_KEY = "your-site-key";

const handleSuccess = (response: string) => {
  console.log("验证成功:", response);
  // 处理验证成功逻辑
};

const handleError = (error: string) => {
  console.error("验证失败:", error);
  // 处理验证失败逻辑
};
</script>
```

## 高级用法

```vue
<template>
  <TurnstileComponent
    ref="turnstileRef"
    :sitekey="TURNSTILE_SITE_KEY"
    theme="dark"
    size="compact"
    appearance="interaction-only"
    :retry-interval="2000"
    refresh-expired="manual"
    @callback="handleSuccess"
    @errorCallback="handleError"
    @expiredCallback="handleExpired"
    @timeoutCallback="handleTimeout"
  />
  
  <div class="actions">
    <button @click="resetTurnstile">重置验证</button>
    <button @click="getResponse">获取响应</button>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TurnstileComponent from "@/components/TurnstileWidget/TurnstileComponent.vue";

const turnstileRef = ref<InstanceType<typeof TurnstileComponent>>();

const resetTurnstile = () => {
  turnstileRef.value?.reset();
};

const getResponse = () => {
  const response = turnstileRef.value?.getResponse();
  console.log("当前响应:", response);
};
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `sitekey` | `string` | - | 站点密钥（必填） |
| `action` | `string` | - | 操作标识符 |
| `cData` | `string` | - | 自定义数据 |
| `execution` | `"render" \| "execute"` | `"render"` | 执行模式 |
| `theme` | `"light" \| "dark" \| "auto"` | `"auto"` | 主题模式 |
| `language` | `string` | `"auto"` | 语言设置 |
| `tabindex` | `number` | `0` | Tab 索引 |
| `responseField` | `string` | - | 响应字段名 |
| `responseFieldName` | `string` | - | 响应字段名称 |
| `size` | `"normal" \| "flexible" \| "compact"` | - | 组件尺寸 |
| `retry` | `"auto" \| "never"` | - | 重试策略 |
| `retryInterval` | `number` | - | 重试间隔（毫秒） |
| `refreshExpired` | `"auto" \| "manual" \| "never"` | - | 过期刷新策略 |
| `refreshTimeout` | `"auto" \| "manual" \| "never"` | - | 超时刷新策略 |
| `appearance` | `"always" \| "execute" \| "interaction-only"` | - | 外观模式 |
| `feedbackEnabled` | `boolean` | - | 是否启用反馈 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `callback` | `response: string` | 验证成功回调 |
| `errorCallback` | `response: string` | 验证错误回调 |
| `expiredCallback` | - | 验证过期回调 |
| `beforeInteractiveCallback` | - | 交互前回调 |
| `afterInteractiveCallback` | - | 交互后回调 |
| `unsupportedCallback` | - | 不支持回调 |
| `timeoutCallback` | - | 超时回调 |

## 暴露的方法

| 方法名 | 返回值 | 说明 |
|--------|--------|------|
| `reset()` | `void` | 重置验证组件 |
| `getResponse()` | `string` | 获取当前验证响应 |

## 注意事项

1. **Site Key**: 使用前需要在 Cloudflare 控制台获取有效的 Site Key
2. **网络环境**: 确保用户网络环境可以访问 Cloudflare 服务
3. **HTTPS**: 生产环境建议使用 HTTPS 协议
4. **API 拼写**: Turnstile API 中的 `appearance` 属性在实际 API 中拼写为 `apperance`（这是 Cloudflare 的拼写错误）

## 样式定制

组件提供了基础的样式，可以通过 CSS 变量或覆盖样式进行定制：

```scss
.turnstile-container {
  // 自定义样式
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```
