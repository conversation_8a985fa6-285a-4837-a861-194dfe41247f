<script setup lang="ts">
/**
 * Cloudflare Turnstile 验证组件
 * 提供人机验证功能，支持多种配置选项和主题
 */

/**
 * 组件属性定义
 */
interface Props {
  /** 站点密钥，必填 */
  sitekey: string;
  /** 操作标识符 */
  action?: string;
  /** 自定义数据 */
  cData?: string;
  /** 执行模式 */
  execution?: "render" | "execute";
  /** 主题模式 */
  theme?: "light" | "dark" | "auto";
  /** 语言设置 */
  language?: string;
  /** Tab 索引 */
  tabindex?: number;
  /** 响应字段名 */
  responseField?: string;
  /** 响应字段名称 */
  responseFieldName?: string;
  /** 组件尺寸 */
  size?: "normal" | "flexible" | "compact";
  /** 重试策略 */
  retry?: "auto" | "never";
  /** 重试间隔（毫秒） */
  retryInterval?: number;
  /** 过期刷新策略 */
  refreshExpired?: "auto" | "manual" | "never";
  /** 超时刷新策略 */
  refreshTimeout?: "auto" | "manual" | "never";
  /** 外观模式 */
  appearance?: "always" | "execute" | "interaction-only";
  /** 是否启用反馈 */
  feedbackEnabled?: boolean;
}

/**
 * 组件事件定义
 */
interface Emits {
  /** 验证成功回调 */
  (e: "callback", response: string): void;
  /** 验证错误回调 */
  (e: "errorCallback", response: string): void;
  /** 验证过期回调 */
  (e: "expiredCallback"): void;
  /** 交互前回调 */
  (e: "beforeInteractiveCallback"): void;
  /** 交互后回调 */
  (e: "afterInteractiveCallback"): void;
  /** 不支持回调 */
  (e: "unsupportedCallback"): void;
  /** 超时回调 */
  (e: "timeoutCallback"): void;
}

const props = withDefaults(defineProps<Props>(), {
  execution: "render",
  theme: "auto",
  language: "auto",
  tabindex: 0,
});

const emit = defineEmits<Emits>();

// 模板引用
const turnstileBox = ref<HTMLDivElement>();

/**
 * 渲染 Turnstile 验证组件
 */
const renderTurnstile = () => {
  (window as any).onloadTurnstileCallback = () => {
    if (turnstileBox.value) {
      window.turnstile?.render(turnstileBox.value, {
        sitekey: props.sitekey,
        callback: (response: string) => emit("callback", response),
        "expired-callback": () => emit("expiredCallback"),
        "error-callback": (response: string) => emit("errorCallback", response),
        action: props.action,
        cData: props.cData,
        execution: props.execution,
        "before-interactive-callback": () => emit("beforeInteractiveCallback"),
        "after-interactive-callback": () => emit("afterInteractiveCallback"),
        "unsupported-callback": () => emit("unsupportedCallback"),
        theme: props.theme,
        language: props.language,
        tabindex: props.tabindex,
        "timeout-callback": () => emit("timeoutCallback"),
        "response-field": props.responseField,
        "response-field-name": props.responseFieldName,
        size: props.size,
        retry: props.retry,
        "retry-interval": props.retryInterval,
        "refresh-expired": props.refreshExpired,
        "refresh-timeout": props.refreshTimeout,
        apperance: props.appearance, // 注意：这里使用 apperance 是因为 Turnstile API 的拼写错误
        "feedback-enabled": props.feedbackEnabled,
      });
    }
  };
};

/**
 * 加载 Turnstile 脚本
 */
const loadTurnstileScript = () => {
  if (window.turnstile === null || !window.turnstile) {
    const script = document.createElement("script");
    script.src =
      "https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback";
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
  }
  renderTurnstile();
};

/**
 * 重置 Turnstile 验证
 */
const reset = () => {
  if (window.turnstile && turnstileBox.value) {
    // Turnstile reset 方法通常需要传入 widget ID 或元素
    (window.turnstile as any).reset(turnstileBox.value);
  }
};

/**
 * 获取验证响应
 */
const getResponse = (): string => {
  if (window.turnstile && turnstileBox.value) {
    // Turnstile getResponse 方法通常需要传入 widget ID
    return (window.turnstile as any).getResponse(turnstileBox.value) || "";
  }
  return "";
};

// 暴露方法给父组件
defineExpose({
  reset,
  getResponse,
});

// 组件挂载时初始化
onMounted(() => {
  loadTurnstileScript();
});
</script>

<template>
  <div ref="turnstileBox" id="turnstile-box" class="turnstile-container"></div>
</template>

<style scoped lang="scss">
.turnstile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 65px; // Turnstile 组件的最小高度
}
</style>
