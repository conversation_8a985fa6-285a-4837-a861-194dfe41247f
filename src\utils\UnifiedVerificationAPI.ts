/**
 * 统一验证 API
 * 基于 TurnstileComponent.vue 组件，兼容 Cloudflare 和 Geetest 验证
 */

import { createApp, h, ref, type App } from "vue";
import { showToast } from "vant";
import TurnstileComponent from "@/components/TurnstileWidget/TurnstileComponent.vue";
import {
  VERIFICATION_TYPE,
  VERIFICATION_SCENE_TYPE,
  VERIFICATION_TYPES,
  type VerificationResult,
  verificationMgr,
} from "./VerificationMgr";
import { GeetestMgr, GEETEST_TYPE } from "./GeetestMgr";
import { CloudflareMgr, CF_TURNSTILE_TYPE } from "./CloudflareMgr";

// 统一验证选项接口
export interface UnifiedVerifyOptions {
  /** 验证场景类型 */
  sceneType: string;
  /** 验证类型（可选，默认使用配置的类型） */
  verificationType?: VERIFICATION_TYPE;
  /** 弹窗标题 */
  title?: string;
  /** 描述文本 */
  description?: string;
  /** 是否显示取消按钮 */
  showCancelButton?: boolean;
  /** Cloudflare 相关配置 */
  cloudflare?: {
    /** 自定义 Site Key */
    siteKey?: string;
    /** 主题 */
    theme?: "light" | "dark" | "auto";
    /** 尺寸 */
    size?: "normal" | "flexible" | "compact";
    /** 外观 */
    appearance?: "always" | "execute" | "interaction-only";
    /** 验证成功后自动关闭延迟时间(ms) */
    autoCloseDelay?: number;
  };
  /** Geetest 相关配置 */
  geetest?: {
    /** 手机号（用于某些验证场景） */
    phone?: string;
  };
}

// 统一验证结果接口
export interface UnifiedVerifyResult {
  /** 验证是否成功 */
  success: boolean;
  /** 验证类型 */
  type: VERIFICATION_TYPE;
  /** 是否被取消 */
  cancelled?: boolean;
  /** 错误信息 */
  error?: string;
  /** 错误码 */
  errorCode?: string;
  /** 是否可重试 */
  retryable?: boolean;
  /** 验证数据 */
  data?: {
    // Geetest 数据
    geetest_guard?: string;
    userInfo?: string;
    geetest_captcha?: string;
    buds?: string;
    // Cloudflare 数据
    "cf-token"?: string;
    "cf-scene"?: string;
    // 向后兼容
    cf_token?: string;
    cf_type?: string;
  };
}

/**
 * 显示统一验证弹窗
 * 根据配置自动选择 Cloudflare 或 Geetest 验证
 * @param options 验证选项
 * @returns Promise<UnifiedVerifyResult>
 */
export function showUnifiedVerification(
  options: UnifiedVerifyOptions
): Promise<UnifiedVerifyResult> {
  return new Promise((resolve) => {
    const verificationType =
      options.verificationType || verificationMgr.getCurrentVerificationType();

    console.log(`🔐 Starting unified verification: ${verificationType} - ${options.sceneType}`);

    if (verificationType === VERIFICATION_TYPE.CLOUDFLARE) {
      showCloudflareVerificationDialog(options, resolve);
    } else if (verificationType === VERIFICATION_TYPE.GEETEST) {
      executeGeetestVerification(options, resolve);
    } else {
      console.error(`❌ Unsupported verification type: ${verificationType}`);
      resolve({
        success: false,
        type: verificationType,
        error: `Unsupported verification type: ${verificationType}`,
      });
    }
  });
}

/**
 * 显示 Cloudflare 验证弹窗（使用 TurnstileComponent）
 */
function showCloudflareVerificationDialog(
  options: UnifiedVerifyOptions,
  resolve: (result: UnifiedVerifyResult) => void
): void {
  // 创建容器元素
  const container = document.createElement("div");
  document.body.appendChild(container);

  // 创建响应式状态
  const visible = ref(true);
  let app: App | null = null;

  // 清理函数
  const cleanup = () => {
    setTimeout(() => {
      if (container && container.parentNode && app) {
        app.unmount();
        container.parentNode.removeChild(container);
        app = null;
      }
    }, 300);
  };

  // 获取 Cloudflare 配置
  const cfType = mapSceneTypeToCFType(options.sceneType);
  const cloudflareManager = CloudflareMgr.instance;
  const siteKeyConfig = options.cloudflare?.siteKey
    ? { siteKey: options.cloudflare.siteKey, mode: "managed" }
    : cloudflareManager.getSiteKey(cfType);

  // 处理验证成功
  const handleSuccess = (response: string) => {
    console.log("✅ Cloudflare verification successful");
    cleanup();
    resolve({
      success: true,
      type: VERIFICATION_TYPE.CLOUDFLARE,
      data: {
        "cf-token": response,
        "cf-scene": cfType,
        cf_token: response,
        cf_type: cfType,
      },
    });
  };

  // 处理验证错误
  const handleError = (error: string) => {
    console.error("❌ Cloudflare verification error:", error);
    cleanup();
    resolve({
      success: false,
      type: VERIFICATION_TYPE.CLOUDFLARE,
      error: error || "Verification failed",
      retryable: true,
    });
  };

  // 处理取消
  const handleCancel = () => {
    console.log("❌ Cloudflare verification cancelled");
    cleanup();
    resolve({
      success: false,
      type: VERIFICATION_TYPE.CLOUDFLARE,
      cancelled: true,
      error: "Verification cancelled",
    });
  };

  // 处理弹窗关闭
  const handleClose = () => {
    visible.value = false;
    handleCancel();
  };

  // 创建弹窗组件
  const DialogComponent = {
    setup() {
      return () =>
        h(
          "div",
          {
            class: "unified-verify-dialog-overlay",
            style: {
              position: "fixed",
              top: "0",
              left: "0",
              right: "0",
              bottom: "0",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: "9999",
            },
            onClick: (e: Event) => {
              if (e.target === e.currentTarget) {
                handleClose();
              }
            },
          },
          [
            h(
              "div",
              {
                class: "unified-verify-dialog",
                style: {
                  backgroundColor: "white",
                  borderRadius: "8px",
                  padding: "24px",
                  minWidth: "320px",
                  maxWidth: "400px",
                  margin: "20px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
                  position: "relative",
                },
              },
              [
                // 标题
                h(
                  "div",
                  {
                    class: "dialog-header",
                    style: {
                      marginBottom: "16px",
                      textAlign: "center",
                    },
                  },
                  [
                    h(
                      "h3",
                      {
                        style: {
                          margin: "0",
                          fontSize: "18px",
                          fontWeight: "600",
                          color: "#333",
                        },
                      },
                      options.title || "Security Verification"
                    ),
                    options.showCancelButton !== false &&
                      h(
                        "button",
                        {
                          class: "close-btn",
                          style: {
                            position: "absolute",
                            top: "16px",
                            right: "16px",
                            background: "none",
                            border: "none",
                            fontSize: "20px",
                            cursor: "pointer",
                            color: "#999",
                          },
                          onClick: handleClose,
                        },
                        "×"
                      ),
                  ]
                ),

                // 描述
                options.description &&
                  h(
                    "div",
                    {
                      class: "dialog-description",
                      style: {
                        marginBottom: "20px",
                        color: "#666",
                        fontSize: "14px",
                        textAlign: "center",
                      },
                    },
                    options.description
                  ),

                // Turnstile 组件
                h(
                  "div",
                  {
                    class: "turnstile-wrapper",
                    style: {
                      marginBottom: "16px",
                    },
                  },
                  [
                    h(TurnstileComponent, {
                      sitekey: siteKeyConfig.siteKey,
                      theme: options.cloudflare?.theme || "auto",
                      size: options.cloudflare?.size || "normal",
                      appearance: options.cloudflare?.appearance || "always",
                      onCallback: handleSuccess,
                      onErrorCallback: handleError,
                      onExpiredCallback: () => handleError("Verification expired"),
                      onTimeoutCallback: () => handleError("Verification timeout"),
                    }),
                  ]
                ),

                // 取消按钮
                options.showCancelButton !== false &&
                  h(
                    "div",
                    {
                      class: "dialog-actions",
                      style: {
                        textAlign: "center",
                      },
                    },
                    [
                      h(
                        "button",
                        {
                          class: "cancel-btn",
                          style: {
                            padding: "8px 16px",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            background: "white",
                            color: "#666",
                            cursor: "pointer",
                            fontSize: "14px",
                          },
                          onClick: handleClose,
                        },
                        "Cancel"
                      ),
                    ]
                  ),
              ]
            ),
          ]
        );
    },
  };

  // 创建并挂载应用
  app = createApp(DialogComponent);
  app.mount(container);
}

/**
 * 执行 Geetest 验证
 */
function executeGeetestVerification(
  options: UnifiedVerifyOptions,
  resolve: (result: UnifiedVerifyResult) => void
): void {
  const geetestCallback = (result: any) => {
    if (result === false) {
      resolve({
        success: false,
        type: VERIFICATION_TYPE.GEETEST,
        error: "Geetest verification failed",
      });
      return;
    }

    resolve({
      success: true,
      type: VERIFICATION_TYPE.GEETEST,
      data: {
        geetest_guard: result.geetest_guard || "",
        userInfo: result.userInfo || "",
        geetest_captcha: result.geetest_captcha || "",
        buds: result.buds || "64",
      },
    });
  };

  // 调用 Geetest 验证
  GeetestMgr.instance.geetest_device(options.sceneType, geetestCallback, options.geetest?.phone);
}

/**
 * 将场景类型映射到 CF_TURNSTILE_TYPE
 */
function mapSceneTypeToCFType(sceneType: string): CF_TURNSTILE_TYPE {
  // 直接匹配枚举值
  const cfTypeMap: Record<string, CF_TURNSTILE_TYPE> = {};
  Object.values(CF_TURNSTILE_TYPE).forEach((value) => {
    cfTypeMap[value] = value as CF_TURNSTILE_TYPE;
  });

  if (cfTypeMap[sceneType]) {
    return cfTypeMap[sceneType];
  }

  // 场景映射
  const sceneMapping: Record<string, CF_TURNSTILE_TYPE> = {
    [VERIFICATION_SCENE_TYPE.password_login]: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
    [VERIFICATION_SCENE_TYPE.phone_code_login]: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
    [VERIFICATION_SCENE_TYPE.phone_login_code]: CF_TURNSTILE_TYPE.LOGIN_PHONE_GET_CODE,
    [VERIFICATION_SCENE_TYPE.forget_password]: CF_TURNSTILE_TYPE.FORGET_PW_SUBMIT,
    [VERIFICATION_SCENE_TYPE.forget_password_code]: CF_TURNSTILE_TYPE.FORGET_PW_GET_CODE,
    [VERIFICATION_SCENE_TYPE.withdraw]: CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT,
    [VERIFICATION_SCENE_TYPE.bind_withdraw_account]:
      CF_TURNSTILE_TYPE.BIND_WITHDRAWAL_ACCOUNT_SUBMIT,
    [VERIFICATION_SCENE_TYPE.change_withdraw_account]:
      CF_TURNSTILE_TYPE.MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT,
  };

  return sceneMapping[sceneType] || CF_TURNSTILE_TYPE.LOGIN_SUBMIT;
}

// 便捷方法：登录验证
export function verifyLogin(): Promise<UnifiedVerifyResult> {
  return showUnifiedVerification({
    sceneType: VERIFICATION_SCENE_TYPE.password_login,
    title: "Login Verification",
    description: "Please complete the security verification to login.",
  });
}

// 便捷方法：获取验证码
export function verifyGetCode(phone?: string): Promise<UnifiedVerifyResult> {
  return showUnifiedVerification({
    sceneType: VERIFICATION_SCENE_TYPE.phone_login_code,
    title: "Get Verification Code",
    description: "Please complete the security verification to get the code.",
    geetest: { phone },
  });
}

// 便捷方法：忘记密码
export function verifyForgetPassword(): Promise<UnifiedVerifyResult> {
  return showUnifiedVerification({
    sceneType: VERIFICATION_SCENE_TYPE.forget_password,
    title: "Password Reset Verification",
    description: "Please complete the security verification to reset your password.",
  });
}

// 便捷方法：提现验证
export function verifyWithdraw(): Promise<UnifiedVerifyResult> {
  return showUnifiedVerification({
    sceneType: VERIFICATION_SCENE_TYPE.withdraw,
    title: "Withdrawal Verification",
    description: "Please complete the security verification to proceed with withdrawal.",
  });
}
