/**
 * 登录相关 Store
 */

import { defineStore } from "pinia";
import { showToast } from "vant";
import router from "@/router";
import { useGlobalStore } from "@/stores/global";
import { GeetestMgr } from "@/utils/GeetestMgr";

import { VerificationMgr, VERIFICATION_TYPE } from "@/utils/VerificationMgr";
import { Md5 } from "@/utils/core/Md5";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { loginManager } from "@/utils/managers/LoginManager";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { LoginMgr } from "@/utils/ThirdPartLoginMsg";

import type {
  LoginStoreState,
  PasswordLoginPayload,
  CodeLoginPayload,
  VerificationCallbackResult,
  LoginType,
  VerificationCallback,
  PasswordLoginParams,
  CodeLoginParams,
} from "@/types/login";

const LOGIN_STORE_START = "LOGIN_TYPE_";

export const useLoginStore = defineStore("login", {
  state: (): LoginStoreState => ({
    userPhone: "",
    currentLoginMode: "code", // "password" | 'code'  无记录首次使用验证码登录，有过密码记录或者密码登录默认使用密码登录
    isCodeInputMode: false,
    isPrivacyAgreed: true,
    isPrivacyDialogVisible: false,
    isLoggingIn: false,
  }),

  getters: {
    /**
     * 是否应该显示关闭按钮
     */
    shouldShowCloseButton(): boolean {
      return (
        this.currentLoginMode === "password" ||
        (this.currentLoginMode === "code" && !this.isCodeInputMode)
      );
    },

    /**
     * 是否应该显示返回按钮
     */
    shouldShowBackButton(): boolean {
      return this.isCodeInputMode;
    },

    /**
     * 是否有第三方登录选项
     */
    hasThirdPartyLogin(): boolean {
      const globalStore = useGlobalStore();
      return (
        globalStore.loginConfig.login_facebook == 0 || globalStore.loginConfig.login_google == 0
      );
    },
  },

  actions: {
    /**
     * 初始化登录页面
     */
    initLoginPage() {
      const globalStore = useGlobalStore();
      // 重置状态
      globalStore.loginOut(false);

      // 恢复手机号
      const phoneNum = getLocalStorage("phone");
      const { userInfo } = globalStore;
      this.currentLoginMode = getLocalStorage(LOGIN_STORE_START + phoneNum);
      // 不支持密码登录或者未设置登录密码 默认为code,其余情况默认保持原记录的登录方式
      if (globalStore.loginConfig.login_password !== 1 || userInfo?.login_password !== 1) {
        this.currentLoginMode = "code";
      }

      // 初始化第三方登录SDK
      this.initThirdPartySDK();

      // 恢复手机号
      if (phoneNum) {
        this.userPhone = phoneNum;
      }
      // 重置下验证码登录的步骤
      this.isCodeInputMode = false;
    },

    /**
     * 初始化第三方登录SDK
     */
    initThirdPartySDK() {
      const globalStore = useGlobalStore();

      if (globalStore.loginConfig.login_facebook == 0) {
        LoginMgr.instance.facebook_init();
      }
      if (globalStore.loginConfig.login_google == 0) {
        LoginMgr.instance.google_init();
      }
    },

    /**
     * 处理验证 (支持 Geetest 和 Cloudflare)
     * @param loginType 登录类型
     * @param callback 验证回调
     * @param useSeamless 是否使用无感验证 (仅对 Cloudflare 有效)
     * 验证码的是有感的，提交的部分是无感的
     */
    async handleVerification(
      loginType: LoginType = "phone_login_code",
      callback: VerificationCallback,
      useSeamless: boolean = false
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        // 使用统一的验证管理器 verifyWithComponent
        await VerificationMgr.instance.verify(
          loginType,
          async (result) => {
            if (result && result.success) {
              // 根据验证类型处理结果
              if (result.type === VERIFICATION_TYPE.GEETEST) {
                // Geetest 验证结果
                await callback({
                  buds: result.data?.buds || "64",
                  geetest_guard: result.data?.geetest_guard || "",
                  userInfo: result.data?.userInfo || "",
                  geetest_captcha: result.data?.geetest_captcha || "",
                });
              } else if (result.type === VERIFICATION_TYPE.CLOUDFLARE) {
                // Cloudflare 验证结果 - 转换为 Geetest 格式以保持兼容性
                await callback({
                  // 添加 Cloudflare 特有的字段
                  "cf-token": result.data?.[`cf-token`] || result.data?.cf_token || "",
                  "cf-scene": result.data?.[`cf-scene`] || result.data?.cf_type || loginType,
                });
              }
            } else {
              console.warn("Verification failed or was cancelled");
              showToast("Verification failed");
            }
          },
          {
            phone: this.userPhone, // 传递手机号给 Geetest
          }
        );
      } catch (error) {
        console.error("Verification failed:", error);
        showToast("Verification failed");
      }
    },

    /**
     * 处理 Geetest 验证 (直接使用 GeetestMgr)
     */
    async handleGeetestVerification(
      loginType: LoginType = "phone_login_code",
      callback: VerificationCallback
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        await GeetestMgr.instance.geetest_device(loginType, async (ret: any) => {
          if (ret) {
            await callback({
              buds: ret?.buds || "64",
              geetest_guard: ret?.geetest_guard || "",
              userInfo: ret?.userInfo || "",
              geetest_captcha: ret?.geetest_captcha || "",
            });
          } else {
            console.warn("Geetest verification failed or was cancelled");
            showToast("Verification failed");
          }
        });
      } catch (error) {
        console.error("Geetest verification failed:", error);
        showToast("Verification failed");
      }
    },
    // 记录登录方式为密码
    setLoginTypeTag(type = "code", phone?: string) {
      if (phone || this.userPhone)
        setLocalStorage(LOGIN_STORE_START + (phone || this.userPhone), type);
    },

    /**
     * 执行用户登录
     */
    async executePlayerLogin(payload: PasswordLoginParams | CodeLoginParams): Promise<void> {
      try {
        this.isLoggingIn = true;
        showZLoading();
        await loginManager.executeLogin(payload);
      } catch (error) {
        console.error("Player login failed:", error);
        throw error;
      } finally {
        this.isLoggingIn = false;
        closeZLoading();
      }
    },

    /**
     * 处理验证 (使用 TurnstileComponent 组件)
     * 模仿 handleVerification 方法，但使用 verifyWithComponent 进行验证
     * @param loginType 登录类型
     * @param callback 验证回调
     * @param useSeamless 是否使用无感验证 (仅对 Cloudflare 有效)
     * @param options 额外的验证选项
     */
    async handleComponentVerification(
      loginType: LoginType = "phone_login_code",
      callback: VerificationCallback,
      useSeamless: boolean = false,
      options?: {
        title?: string;
        description?: string;
        showCancelButton?: boolean;
        cloudflare?: {
          theme?: "light" | "dark" | "auto";
          size?: "normal" | "flexible" | "compact";
          appearance?: "always" | "execute" | "interaction-only";
        };
      }
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        // 使用基于 TurnstileComponent 的验证管理器
        await VerificationMgr.instance.verifyWithComponent(
          loginType,
          async (result) => {
            if (result && result.success) {
              // 根据验证类型处理结果
              if (result.type === VERIFICATION_TYPE.GEETEST) {
                // Geetest 验证结果
                await callback({
                  buds: result.data?.buds || "64",
                  geetest_guard: result.data?.geetest_guard || "",
                  userInfo: result.data?.userInfo || "",
                  geetest_captcha: result.data?.geetest_captcha || "",
                });
              } else if (result.type === VERIFICATION_TYPE.CLOUDFLARE) {
                // Cloudflare 验证结果 - 转换为 Geetest 格式以保持兼容性
                await callback({
                  // 添加 Cloudflare 特有的字段
                  "cf-token": result.data?.[`cf-token`] || result.data?.cf_token || "",
                  "cf-scene": result.data?.[`cf-scene`] || result.data?.cf_type || loginType,
                });
              }
            } else {
              console.warn("Component verification failed or was cancelled");
              showToast("Verification failed");
            }
          },
          {
            phone: this.userPhone, // 传递手机号给 Geetest
            // 扩展配置选项
            title: options?.title,
            description: options?.description,
            showCancelButton: options?.showCancelButton,
            cloudflare: options?.cloudflare,
          }
        );
      } catch (error) {
        console.error("Component verification failed:", error);
        showToast("Verification failed");
      }
    },

    /**
     * 处理密码登录
     */
    async handlePasswordLogin(payload: PasswordLoginPayload): Promise<void> {
      const { password, phone: phoneNum } = payload;

      if (!password) {
        throw new Error("Password is required");
      }

      return new Promise((resolve, reject) => {
        this.handleVerification(
          "password_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: PasswordLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                password: Md5.hashStr(password).toString(),
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("password");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true // 使用无感验证
        );
      });
    },

    /**
     * 处理密码登录 (使用 TurnstileComponent 组件)
     * 模仿 handlePasswordLogin 方法，但使用 handleComponentVerification 进行验证
     */
    async handlePasswordLoginWithComponent(
      payload: PasswordLoginPayload,
      options?: {
        title?: string;
        description?: string;
        cloudflare?: {
          theme?: "light" | "dark" | "auto";
          size?: "normal" | "flexible" | "compact";
          appearance?: "always" | "execute" | "interaction-only";
        };
      }
    ): Promise<void> {
      const { password, phone: phoneNum } = payload;

      if (!password) {
        throw new Error("Password is required");
      }

      return new Promise((resolve, reject) => {
        this.handleComponentVerification(
          "password_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: PasswordLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                password: Md5.hashStr(password).toString(),
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("password");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true, // 使用无感验证
          {
            title: options?.title || "Password Login Verification",
            description:
              options?.description ||
              "Please complete the security verification to login with password.",
            showCancelButton: true,
            cloudflare: options?.cloudflare,
          }
        );
      });
    },

    /**
     * 处理验证码登录
     */
    async handleCodeLogin(payload: CodeLoginPayload): Promise<void> {
      const { verCode, phone: phoneNum } = payload;

      return new Promise((resolve, reject) => {
        this.handleVerification(
          "phone_code_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: CodeLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                verifyCode: verCode,
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("code");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true // 使用无感验证
        );
      });
    },

    /**
     * 处理验证码登录 (使用 TurnstileComponent 组件)
     * 模仿 handleCodeLogin 方法，但使用 handleComponentVerification 进行验证
     */
    async handleCodeLoginWithComponent(
      payload: CodeLoginPayload,
      options?: {
        title?: string;
        description?: string;
        cloudflare?: {
          theme?: "light" | "dark" | "auto";
          size?: "normal" | "flexible" | "compact";
          appearance?: "always" | "execute" | "interaction-only";
        };
      }
    ): Promise<void> {
      const { verCode, phone: phoneNum } = payload;

      return new Promise((resolve, reject) => {
        this.handleComponentVerification(
          "phone_code_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: CodeLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                verifyCode: verCode,
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("code");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true, // 使用无感验证
          {
            title: options?.title || "Code Login Verification",
            description:
              options?.description ||
              "Please complete the security verification to login with verification code.",
            showCancelButton: true,
            cloudflare: options?.cloudflare,
          }
        );
      });
    },

    /**
     * 处理Google登录
     */
    tapGoogleLogin() {
      LoginMgr.instance.google_login();
    },

    async handleGoogleLogin(idToken?) {
      const params = {
        googleToken: idToken,
        login_type: "google",
        google_redirect_uri: import.meta.env.VITE_WEB_URL,
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 处理Facebook登录
     */
    tapFacebookLogin() {
      LoginMgr.instance.facebook_login();
    },
    async handleFacebookLogin(accessToken?, userID?) {
      const params = {
        accessToken: accessToken,
        faceUserId: userID,
        login_type: "facebook",
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 切换到验证码登录
     */
    switchToCodeLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "code";
      setLocalStorage(LOGIN_STORE_START + phoneNum, "code");
    },

    /**
     * 切换到密码登录
     */
    switchToPasswordLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "password";
      setLocalStorage(LOGIN_STORE_START + phoneNum, "password");
    },

    /**
     * 处理验证码页面变化
     */
    handleCodePageChange(showCodeInput: boolean) {
      this.isCodeInputMode = showCodeInput;
    },

    /**
     * 处理返回操作
     */
    handleGoBack() {
      this.isCodeInputMode = false;
    },

    /**
     * 处理关闭页面
     */
    handleClosePage() {
      router.replace("/home");
      // router.back();
    },

    /**
     * 处理页面导航
     */
    handleNavigateToPage(path: string) {
      router.push(path);
    },

    /**
     * 处理隐私协议状态变化
     */
    handleAgreementChange(accepted: boolean) {
      this.isPrivacyAgreed = accepted;
    },

    /**
     * 处理隐私政策确认
     */
    handlePrivacyConfirm() {
      this.isPrivacyAgreed = true;
      this.isPrivacyDialogVisible = false;
    },

    /**
     * 重置登录状态
     */
    resetLoginState() {
      this.isCodeInputMode = false;
      this.isPrivacyDialogVisible = false;
      this.isLoggingIn = false;
    },
  },
});
