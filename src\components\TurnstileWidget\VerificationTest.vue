<template>
  <div class="verification-test">
    <h2>验证系统测试</h2>
    
    <div class="test-section">
      <h3>测试新的 verifyWithComponent 方法</h3>
      <p>这个测试验证了新创建的 verifyWithComponent 方法是否正常工作。</p>
      
      <div class="button-group">
        <button @click="testComponentVerification" :disabled="isLoading">
          {{ isLoading ? "测试中..." : "测试组件验证" }}
        </button>
        <button @click="testOriginalVerification" :disabled="isLoading">
          {{ isLoading ? "测试中..." : "测试原始验证" }}
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试统一验证 API</h3>
      <p>测试直接使用 UnifiedVerificationAPI 的功能。</p>
      
      <div class="button-group">
        <button @click="testUnifiedAPI" :disabled="isLoading">
          {{ isLoading ? "测试中..." : "测试统一 API" }}
        </button>
        <button @click="testConvenienceMethods" :disabled="isLoading">
          {{ isLoading ? "测试中..." : "测试便捷方法" }}
        </button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试结果</h3>
      <div class="result-display">
        <div v-if="testResults.length > 0">
          <div v-for="(result, index) in testResults" :key="index" class="test-result">
            <div class="result-header">
              <span :class="['status', result.success ? 'success' : 'failed']">
                {{ result.success ? '✅' : '❌' }}
              </span>
              <span class="test-name">{{ result.testName }}</span>
              <span class="timestamp">{{ result.timestamp }}</span>
            </div>
            <div class="result-details">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <p v-else class="no-results">暂无测试结果</p>
      </div>
    </div>

    <div class="test-section">
      <h3>导入测试</h3>
      <p>验证所有必要的模块都能正确导入。</p>
      <div class="import-status">
        <div class="import-item">
          <span class="import-name">VerificationMgr:</span>
          <span :class="['import-status', verificationMgrImported ? 'success' : 'failed']">
            {{ verificationMgrImported ? '✅ 成功' : '❌ 失败' }}
          </span>
        </div>
        <div class="import-item">
          <span class="import-name">UnifiedVerificationAPI:</span>
          <span :class="['import-status', unifiedAPIImported ? 'success' : 'failed']">
            {{ unifiedAPIImported ? '✅ 成功' : '❌ 失败' }}
          </span>
        </div>
        <div class="import-item">
          <span class="import-name">TurnstileComponent:</span>
          <span :class="['import-status', turnstileComponentImported ? 'success' : 'failed']">
            {{ turnstileComponentImported ? '✅ 成功' : '❌ 失败' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { showToast } from "vant";

// 状态管理
const isLoading = ref(false);
const testResults = ref<Array<{
  testName: string;
  success: boolean;
  data: any;
  timestamp: string;
}>>([]);

// 导入状态
const verificationMgrImported = ref(false);
const unifiedAPIImported = ref(false);
const turnstileComponentImported = ref(false);

// 添加测试结果
const addTestResult = (testName: string, success: boolean, data: any) => {
  testResults.value.unshift({
    testName,
    success,
    data,
    timestamp: new Date().toLocaleTimeString(),
  });
  
  // 限制结果数量
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }
};

// 测试组件验证方法
const testComponentVerification = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    // 动态导入以测试导入是否正常
    const { executeComponentVerification } = await import("@/utils/VerificationMgr");
    
    await executeComponentVerification(
      "password_login",
      (result) => {
        if (result && typeof result === 'object' && result.success) {
          addTestResult("组件验证方法", true, result);
          showToast("组件验证测试成功");
        } else {
          addTestResult("组件验证方法", false, result);
          showToast("组件验证测试失败");
        }
      }
    );
  } catch (error) {
    console.error("组件验证测试失败:", error);
    addTestResult("组件验证方法", false, { error: error.message });
    showToast("组件验证测试出错");
  } finally {
    isLoading.value = false;
  }
};

// 测试原始验证方法
const testOriginalVerification = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    const { executeVerification } = await import("@/utils/VerificationMgr");
    
    await executeVerification(
      "password_login",
      (result) => {
        if (result && typeof result === 'object' && result.success) {
          addTestResult("原始验证方法", true, result);
          showToast("原始验证测试成功");
        } else {
          addTestResult("原始验证方法", false, result);
          showToast("原始验证测试失败");
        }
      }
    );
  } catch (error) {
    console.error("原始验证测试失败:", error);
    addTestResult("原始验证方法", false, { error: error.message });
    showToast("原始验证测试出错");
  } finally {
    isLoading.value = false;
  }
};

// 测试统一 API
const testUnifiedAPI = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    const { showUnifiedVerification } = await import("@/utils/UnifiedVerificationAPI");
    
    const result = await showUnifiedVerification({
      sceneType: "password_login",
      title: "测试验证",
      description: "这是一个测试验证",
    });
    
    addTestResult("统一验证 API", result.success, result);
    showToast(result.success ? "统一 API 测试成功" : "统一 API 测试失败");
  } catch (error) {
    console.error("统一 API 测试失败:", error);
    addTestResult("统一验证 API", false, { error: error.message });
    showToast("统一 API 测试出错");
  } finally {
    isLoading.value = false;
  }
};

// 测试便捷方法
const testConvenienceMethods = async () => {
  if (isLoading.value) return;
  
  isLoading.value = true;
  
  try {
    const { verifyLogin } = await import("@/utils/UnifiedVerificationAPI");
    
    const result = await verifyLogin();
    
    addTestResult("便捷方法 (verifyLogin)", result.success, result);
    showToast(result.success ? "便捷方法测试成功" : "便捷方法测试失败");
  } catch (error) {
    console.error("便捷方法测试失败:", error);
    addTestResult("便捷方法 (verifyLogin)", false, { error: error.message });
    showToast("便捷方法测试出错");
  } finally {
    isLoading.value = false;
  }
};

// 测试导入
const testImports = async () => {
  try {
    // 测试 VerificationMgr 导入
    const verificationMgr = await import("@/utils/VerificationMgr");
    verificationMgrImported.value = !!verificationMgr.executeComponentVerification;
    
    // 测试 UnifiedVerificationAPI 导入
    const unifiedAPI = await import("@/utils/UnifiedVerificationAPI");
    unifiedAPIImported.value = !!unifiedAPI.showUnifiedVerification;
    
    // 测试 TurnstileComponent 导入
    const turnstileComponent = await import("@/components/TurnstileWidget/TurnstileComponent.vue");
    turnstileComponentImported.value = !!turnstileComponent.default;
    
    console.log("导入测试完成:", {
      verificationMgr: verificationMgrImported.value,
      unifiedAPI: unifiedAPIImported.value,
      turnstileComponent: turnstileComponentImported.value,
    });
  } catch (error) {
    console.error("导入测试失败:", error);
  }
};

onMounted(() => {
  console.log("验证测试组件已加载");
  testImports();
});
</script>

<style scoped lang="scss">
.verification-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  
  h2 {
    color: #333;
    margin-bottom: 20px;
  }
  
  h3 {
    color: #555;
    margin-bottom: 10px;
  }
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  
  p {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  
  button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background: #007bff;
    color: white;
    cursor: pointer;
    transition: background 0.3s;
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
    
    &:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  }
}

.result-display {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  
  .no-results {
    color: #999;
    text-align: center;
    margin: 0;
  }
}

.test-result {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #f9f9f9;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    
    .status {
      font-size: 16px;
      
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
    
    .test-name {
      font-weight: bold;
      color: #333;
    }
    
    .timestamp {
      color: #666;
      font-size: 12px;
      margin-left: auto;
    }
  }
  
  .result-details {
    background: white;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 8px;
    
    pre {
      margin: 0;
      font-family: 'Courier New', monospace;
      font-size: 11px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.import-status {
  .import-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    
    .import-name {
      font-weight: 500;
      color: #333;
      min-width: 200px;
    }
    
    .import-status {
      &.success {
        color: #28a745;
      }
      
      &.failed {
        color: #dc3545;
      }
    }
  }
}
</style>
