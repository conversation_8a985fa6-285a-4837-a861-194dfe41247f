/**
 * Cloudflare Turnstile 验证管理器
 * 专注于弹窗验证，简化代码结构
 */

// Cloudflare Turnstile Site Key 配置映射
export const SITE_KEY_MAP = {
  // 本地开发环境
  LOCAL: {
    mode: "managed",
    sitekey: "1x00000000000000000000AA",
    // 1x00000000000000000000AA  2x00000000000000000000AB  3x00000000000000000000FF
  },
  // 登录场景 - 有感校验
  SCENE_LOGIN: {
    mode: "managed",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 获取验证码场景 -  无感校验
  SCENE_GET_CODE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // 忘记密码获取验证码 - 无感校验
  SCENE_FORGET_PW_GET_CODE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // 忘记密码提交 - 有感校验
  SCENE_FORGET_PASSWORD: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 首次设定登录密码 - 有感校验
  SCENE_FIRST_PASSWORD: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 首次设定支付密码 - 有感校验
  SCENE_FIRST_PAY_PASSWORD: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 修改登录密码获取验证码 - 无感校验
  SCENE_MODIFY_LOGIN_PW_GET_CODE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // 修改登录密码提交 - 有感校验
  SCENE_CHANGE_PASSWORD: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 修改支付密码提交 - 有感校验
  SCENE_CHANGE_PAY_PASSWORD: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 绑定提款账号提交 - 有感校验
  SCENE_BIND_WITHDRAW_ACCOUNT: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 修改提款账号提交 - 有感校验
  SCENE_CHANGE_WITHDRAW_ACCOUNT: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 提现提交 - 有感校验
  SCENE_WITHDRAW: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 绑定手机号提交 - 有感校验
  SCENE_BIND_PT_PHONE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 修改手机号获取验证码 - 无感校验
  SCENE_MODIFY_PHONE_GET_CODE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6n02z8VbwKkph",
  },
  // 修改手机号提交 - 有感校验
  SCENE_CHANGE_PT_PHONE: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // KYC 提交 - 有感校验
  SCENE_SUB_KYC_INFO: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
  // 注册提交 - 有感校验
  SCENE_REGISTER: {
    mode: "invisible",
    sitekey: "0x4AAAAAABr6liO_iAPr4Zx_",
  },
};

// CF Turnstile 验证场景类型
export enum CF_TURNSTILE_TYPE {
  /** 无 */
  NONE = "",
  /** 手机号注册登录-获取验证码 */
  LOGIN_PHONE_GET_CODE = "SCENE_GET_CODE",
  /** 登录-提交 */
  LOGIN_SUBMIT = "SCENE_LOGIN",
  /** 忘记密码-获取验证码 */
  FORGET_PW_GET_CODE = "SCENE_FORGET_PW_GET_CODE",
  /** 忘记密码-提交 */
  FORGET_PW_SUBMIT = "SCENE_FORGET_PASSWORD",
  /** 首次设定登录密码 */
  FIRST_SET_LOGIN_PW = "SCENE_FIRST_PASSWORD",
  /** 首次设定支付密码 */
  FIRST_SET_PAY_PW = "SCENE_FIRST_PAY_PASSWORD",
  /** 修改登录密码-获取验证码 */
  MODIFY_LOGIN_PW_GET_CODE = "SCENE_MODIFY_LOGIN_PW_GET_CODE",
  /** 修改登录密码-提交 */
  MODIFY_LOGIN_PW_SUBMIT = "SCENE_CHANGE_PASSWORD",
  /** 修改支付密码-获取验证码 */
  MODIFY_PAY_PW_GET_CODE = "xxx",
  /** 修改支付密码-提交 */
  MODIFY_PAY_PW_SUBMIT = "SCENE_CHANGE_PAY_PASSWORD",
  /** 绑定提款账号-获取验证码 */
  BIND_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 绑定提款账号-提交 */
  BIND_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_BIND_WITHDRAW_ACCOUNT",
  /** 修改提款账号-获取验证码 */
  MODIFY_WITHDRAWAL_ACCOUNT_GET_CODE = "xxx",
  /** 修改提款账号-提交 */
  MODIFY_WITHDRAWAL_ACCOUNT_SUBMIT = "SCENE_CHANGE_WITHDRAW_ACCOUNT",
  /** 提现-提交订单 */
  WITHDRAWAL_SUBMIT = "SCENE_WITHDRAW",
  /** 绑定手机号-获取验证码 */
  BIND_PHONE_GET_CODE = "xxx",
  /** 绑定手机号-提交 */
  BIND_PHONE_SUBMIT = "SCENE_BIND_PT_PHONE",
  /** 修改手机号-获取验证码 */
  MODIFY_PHONE_GET_CODE = "SCENE_MODIFY_PHONE_GET_CODE",
  /** 修改手机号-提交 */
  MODIFY_PHONE_SUBMIT = "SCENE_CHANGE_PT_PHONE",
  /** KYC 提交 */
  KYC_SUBMIT = "SCENE_SUB_KYC_INFO",
  /** 注册提交 */
  REGISTER_SUBMIT = "SCENE_REGISTER",
}

// Turnstile 验证结果接口
export interface TurnstileResult {
  success: boolean;
  "cf-token"?: string;
  "cf-scene": string;
  error?: string;
  errorCode?: string; // 添加错误码字段
  retryable?: boolean; // 添加是否可重试字段
}

// Turnstile 配置接口
export interface TurnstileConfig {
  siteKey?: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact";
  language?: string;
  appearance?: "always" | "execute" | "interaction-only";
}

// 验证回调函数类型
export type VerifyCallback = (result: TurnstileResult | false) => void;

// CF 错误码处理工具
export class CFErrorHandler {
  /**
   * 解析 CF 错误码并返回详细信息
   */
  static parseError(error: string): {
    code: string;
    message: string;
    retryable: boolean;
    userFriendlyMessage: string;
  } {
    // 提取错误码 (例如: "600010: The generic_challenge_failure")
    const errorCodeMatch = error.match(/^(\d{6})/);
    const errorCode = errorCodeMatch ? errorCodeMatch[1] : error;

    // 根据错误码分类处理
    const errorInfo = this.getErrorInfo(errorCode);

    return {
      code: errorCode,
      message: error,
      retryable: errorInfo.retryable,
      userFriendlyMessage: errorInfo.userFriendlyMessage,
    };
  }

  /**
   * 获取错误码对应的信息
   */
  private static getErrorInfo(errorCode: string): {
    retryable: boolean;
    userFriendlyMessage: string;
  } {
    // 600*** 系列错误 - Challenge execution failure
    if (errorCode.startsWith("600")) {
      return {
        retryable: true,
        userFriendlyMessage: "Verification failed. Please try again or refresh the page.",
      };
    }

    // 300*** 系列错误 - Generic client execution error
    if (errorCode.startsWith("300")) {
      return {
        retryable: true,
        userFriendlyMessage: "Verification error occurred. Please try again.",
      };
    }

    // 110*** 系列错误 - Configuration issues
    if (errorCode.startsWith("110")) {
      return {
        retryable: false,
        userFriendlyMessage: "Verification service configuration error. Please contact support.",
      };
    }

    // 100*** 系列错误 - Initialization problems
    if (errorCode.startsWith("100")) {
      return {
        retryable: true,
        userFriendlyMessage: "Verification initialization failed. Please refresh the page.",
      };
    }

    // 200*** 系列错误 - Client-side issues
    if (errorCode.startsWith("200")) {
      return {
        retryable: true,
        userFriendlyMessage:
          "Browser or network issue detected. Please check your connection and try again.",
      };
    }

    // 默认处理
    return {
      retryable: true,
      userFriendlyMessage: "Verification failed. Please try again.",
    };
  }

  /**
   * 获取针对特定错误码的建议解决方案
   */
  static getSuggestions(errorCode: string): string[] {
    switch (errorCode) {
      case "600010":
        return [
          "Clear your browser cache and cookies",
          "Disable browser extensions temporarily",
          "Try using a different browser",
          "Check your internet connection",
          "Disable VPN or proxy if enabled",
        ];
      case "110500":
        return [
          "Update your browser to the latest version",
          "Try using a supported browser (Chrome, Firefox, Safari, Edge)",
        ];
      case "200010":
        return ["Clear your browser cache", "Refresh the page and try again"];
      case "200100":
        return ["Check your system clock is set correctly", "Ensure your timezone is accurate"];
      default:
        return [
          "Refresh the page and try again",
          "Clear browser cache and cookies",
          "Try using a different browser",
        ];
    }
  }
}

/**
 * Cloudflare Turnstile 管理器
 * 专注于弹窗验证，简化架构
 */
export class CloudflareMgr {
  private static _instance: CloudflareMgr;
  private isScriptLoaded = false;
  private loadingPromise: Promise<boolean> | null = null;

  // 单例模式
  static get instance(): CloudflareMgr {
    if (!this._instance) {
      this._instance = new CloudflareMgr();
    }
    return this._instance;
  }

  constructor() {
    this.initTurnstileScript();
  }

  /**
   * 初始化 Turnstile 脚本
   */
  private async initTurnstileScript(): Promise<boolean> {
    if (this.isScriptLoaded) {
      return true;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise((resolve) => {
      // 检查是否已经加载
      if (window.turnstile) {
        this.isScriptLoaded = true;
        resolve(true);
        return;
      }

      // 创建脚本标签
      const script = document.createElement("script");
      script.src = "https://challenges.cloudflare.com/turnstile/v0/api.js";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        this.isScriptLoaded = true;
        resolve(true);
      };

      script.onerror = () => {
        this.loadingPromise = null;
        resolve(false);
      };

      document.head.appendChild(script);
    });

    return this.loadingPromise;
  }

  /**
   * 根据 CF_TURNSTILE_TYPE 获取 Site Key 配置
   * @param cfType CF_TURNSTILE_TYPE 验证类型
   * @returns 包含 siteKey 和 mode 的配置对象
   */
  public getSiteKey(cfType?: CF_TURNSTILE_TYPE): { siteKey: string; mode: string } {
    console.log("🔑 getSiteKey called with cfType:", cfType);

    // 如果提供了 cfType，优先从 SITE_KEY_MAP 获取配置
    if (cfType) {
      const sceneKey = cfType as string;
      console.log("🔍 Looking for sceneKey:", sceneKey);
      console.log("🗺️ Available SITE_KEY_MAP keys:", Object.keys(SITE_KEY_MAP));

      const siteKeyConfig = SITE_KEY_MAP[sceneKey as keyof typeof SITE_KEY_MAP];
      console.log("📋 Found siteKeyConfig:", siteKeyConfig);

      if (siteKeyConfig) {
        // 验证 Site Key 格式
        if (!this.validateSiteKey(siteKeyConfig.sitekey)) {
          throw new Error(`Invalid Site Key format from SITE_KEY_MAP: ${siteKeyConfig.sitekey}`);
        }

        const result = {
          siteKey: siteKeyConfig.sitekey,
          mode: siteKeyConfig.mode,
        };
        console.log("✅ Returning siteKey config:", result);
        return result;
      } else {
        console.log("❌ No config found for sceneKey, falling back to LOCAL");
      }
    }

    const fallbackResult = {
      siteKey: SITE_KEY_MAP.LOCAL.sitekey,
      mode: SITE_KEY_MAP.LOCAL.mode,
    };
    console.log("🏠 Returning LOCAL siteKey config:", fallbackResult);
    return fallbackResult;
  }

  /**
   * 获取验证模式
   * @param cfType CF_TURNSTILE_TYPE 验证类型
   * @returns 验证模式：'invisible' 表示无感校验，'managed' 表示有感校验
   */
  public getVerificationMode(cfType: CF_TURNSTILE_TYPE): string {
    const config = this.getSiteKey(cfType);
    return config.mode;
  }

  /**
   * 判断是否为无感校验
   * @param cfType CF_TURNSTILE_TYPE 验证类型
   * @returns true 表示无感校验，false 表示有感校验
   */
  public isInvisibleVerification(cfType: CF_TURNSTILE_TYPE): boolean {
    return this.getVerificationMode(cfType) === "invisible";
  }

  /**
   * 验证 Site Key 格式
   */
  private validateSiteKey(siteKey: string): boolean {
    if (!siteKey || typeof siteKey !== "string") {
      return false;
    }

    // Cloudflare Site Key 格式支持:
    // 1. 生产环境: 0x4 开头的 Site Key
    // 2. 测试环境: 1x, 2x, 3x 开头的测试 Site Key
    const productionPattern = /^0x4[A-Za-z0-9_-]{20,25}$/;
    const testPattern = /^[1-3]x[A-Fa-f0-9]{20,25}$/;
    return productionPattern.test(siteKey) || testPattern.test(siteKey);
  }

  /**
   * 渲染 Turnstile 验证组件
   * @param containerId 容器ID
   * @param cfType 验证类型
   * @param callback 回调函数
   * @param config 配置选项
   */
  public async renderTurnstile(
    containerId: string,
    cfType: CF_TURNSTILE_TYPE,
    callback: VerifyCallback,
    config?: TurnstileConfig
  ): Promise<string | null> {
    try {
      // 确保脚本已加载
      const scriptLoaded = await this.initTurnstileScript();
      if (!scriptLoaded) {
        throw new Error("Failed to load Turnstile script");
      }

      // 获取容器
      const container = document.getElementById(containerId);
      if (!container) {
        throw new Error(`Container with ID '${containerId}' not found`);
      }

      // 清空容器
      container.innerHTML = "";

      // 获取配置
      const siteKeyConfig = config?.siteKey
        ? { siteKey: config.siteKey, mode: "managed" }
        : this.getSiteKey(cfType);
      const siteKey = siteKeyConfig.siteKey;

      // 调试信息
      console.log("🔧 CloudflareMgr.renderTurnstile:");
      console.log("  - cfType:", cfType);
      console.log("  - config.siteKey:", config?.siteKey);
      console.log("  - siteKeyConfig:", siteKeyConfig);
      console.log("  - final siteKey:", siteKey);

      // 构建 Turnstile 选项
      const turnstileOptions = {
        sitekey: siteKey,
        theme: config?.theme || "light",
        size: config?.size || "normal",
        language: config?.language || "en",
        appearance: config?.appearance || "always",
        callback: (token: string) => {
          const result: TurnstileResult = {
            success: true,
            "cf-token": token,
            "cf-scene": cfType,
          };
          callback(result);
        },
        "error-callback": (error: string) => {
          // 解析错误码并获取详细信息
          const errorInfo = CFErrorHandler.parseError(error);

          const result: TurnstileResult = {
            success: false,
            "cf-scene": cfType,
            error: errorInfo.userFriendlyMessage,
            errorCode: errorInfo.code,
            retryable: errorInfo.retryable,
          };

          callback(result);
        },
        "expired-callback": () => {
          const result: TurnstileResult = {
            success: false,
            "cf-scene": cfType,
            error: "expired",
          };
          callback(result);
        },
      };

      // 渲染 Turnstile 组件
      if (!window.turnstile) {
        throw new Error("Turnstile is not loaded");
      }
      const widgetId = window.turnstile.render(container, turnstileOptions);

      return widgetId;
    } catch (error) {
      callback(false);
      return null;
    }
  }

  /**
   * 重置 Turnstile 组件
   */
  public reset(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.reset(widgetId);
    }
  }

  /**
   * 移除 Turnstile 组件
   */
  public remove(widgetId: string): void {
    if (widgetId && window.turnstile) {
      window.turnstile.remove(widgetId);
    }
  }

  /**
   * 获取当前 token (兼容旧版本API)
   */
  getToken(): string {
    // 新版本的 token 在验证回调中返回，这里返回空字符串
    return "";
  }

  /**
   * 清除当前 token (兼容旧版本API)
   */
  clearToken(): void {
    // 新版本的 token 不持久化，无需清除
  }

  /**
   * 测试本地 Site Key 配置 (仅用于开发调试)
   */
  public testLocalSiteKeyConfig(): boolean {
    const env = import.meta.env.MODE;
    const localSiteKey = SITE_KEY_MAP.LOCAL.sitekey;
    const currentSiteKeyConfig = this.getSiteKey();

    if (localSiteKey && env === "development") {
      return currentSiteKeyConfig.siteKey === localSiteKey;
    }
    return false;
  }

  /**
   * 测试 Site Key 验证方法 (仅用于开发调试)
   */
  public testSiteKeyValidation(): Array<{ siteKey: string; isValid: boolean }> {
    const testCases = [
      // 测试环境 Site Keys (应该通过)
      "1x00000000000000000000AA", // 总是通过
      "2x00000000000000000000AB", // 总是失败
      "3x00000000000000000000FF", // 总是显示交互式挑战
      // 生产环境 Site Keys (应该通过)
      "0x4AAAAAAA12345678901234567",
      "0x4AAAAAAA98765432109876543",
      // 无效的 Site Keys (应该失败)
      "invalid-site-key",
      "0x3AAAAAAA12345678901234567", // 错误的前缀
      "4x00000000000000000000AA", // 错误的测试前缀
      "",
      null as any,
      undefined as any,
    ];

    return testCases.map((siteKey) => ({
      siteKey,
      isValid: this.validateSiteKey(siteKey),
    }));
  }
}

// 声明全局 turnstile 对象
declare global {
  interface Window {
    turnstile?: {
      render: (container: HTMLElement, options: any) => string;
      reset: (widgetId: string) => void;
      remove: (widgetId: string) => void;
      getResponse: (widgetId: string) => string;
    };
  }
}
